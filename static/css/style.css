* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

header h1 {
    text-align: center;
    margin-bottom: 20px;
    color: #4a5568;
    font-size: 2.5rem;
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.model-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

#current-model {
    font-weight: bold;
    color: #2d3748;
}

#memory-usage {
    font-size: 0.9rem;
    color: #718096;
}

.controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

select, button {
    padding: 10px 15px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

select {
    background: white;
    border: 2px solid #e2e8f0;
    color: #4a5568;
}

button {
    background: #4299e1;
    color: white;
    font-weight: 500;
}

button:hover:not(:disabled) {
    background: #3182ce;
    transform: translateY(-1px);
}

button:disabled {
    background: #a0aec0;
    cursor: not-allowed;
    transform: none;
}

main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    height: 70vh;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    margin-bottom: 20px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    background: #f7fafc;
}

.welcome-message {
    text-align: center;
    color: #4a5568;
}

.welcome-message h3 {
    margin-bottom: 10px;
    color: #2d3748;
}

.model-cards {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 30px;
    flex-wrap: wrap;
}

.model-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    min-width: 200px;
}

.model-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    border-color: #4299e1;
}

.model-card h4 {
    color: #2d3748;
    margin-bottom: 8px;
}

.model-card p {
    color: #718096;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.model-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.message {
    margin-bottom: 15px;
    padding: 15px;
    border-radius: 10px;
    max-width: 80%;
    word-wrap: break-word;
}

.user-message {
    background: #4299e1;
    color: white;
    margin-left: auto;
    border-bottom-right-radius: 5px;
}

.assistant-message {
    background: #e2e8f0;
    color: #2d3748;
    margin-right: auto;
    border-bottom-left-radius: 5px;
}

.chat-input-container {
    border-top: 2px solid #e2e8f0;
    padding-top: 20px;
}

.input-wrapper {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

#chat-input {
    flex: 1;
    padding: 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    resize: vertical;
    font-family: inherit;
    font-size: 14px;
}

#chat-input:focus {
    outline: none;
    border-color: #4299e1;
}

#send-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 15px 20px;
    background: #48bb78;
}

#send-btn:hover:not(:disabled) {
    background: #38a169;
}

.input-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#clear-chat-btn {
    background: #f56565;
    padding: 8px 15px;
    font-size: 12px;
}

#clear-chat-btn:hover {
    background: #e53e3e;
}

#typing-indicator {
    color: #718096;
    font-style: italic;
    font-size: 0.9rem;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    background: white;
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.status-loaded { background: #c6f6d5; color: #22543d; }
.status-not-loaded { background: #fed7d7; color: #742a2a; }
.status-loading { background: #fef5e7; color: #744210; }

@media (max-width: 768px) {
    .status-bar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .controls {
        justify-content: center;
    }
    
    .model-cards {
        flex-direction: column;
        align-items: center;
    }
    
    .message {
        max-width: 95%;
    }
}
