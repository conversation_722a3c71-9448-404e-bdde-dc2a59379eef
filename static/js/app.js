class ChatInterface {
    constructor() {
        this.socket = io();
        this.currentModel = null;
        this.isTyping = false;
        this.currentResponse = '';
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadModels();
        this.updateStatus();
        
        // Update status every 5 seconds
        setInterval(() => this.updateStatus(), 5000);
    }
    
    initializeElements() {
        this.modelSelect = document.getElementById('model-select');
        this.loadModelBtn = document.getElementById('load-model-btn');
        this.unloadModelBtn = document.getElementById('unload-model-btn');
        this.chatMessages = document.getElementById('chat-messages');
        this.chatInput = document.getElementById('chat-input');
        this.sendBtn = document.getElementById('send-btn');
        this.clearChatBtn = document.getElementById('clear-chat-btn');
        this.currentModelSpan = document.getElementById('current-model');
        this.memoryUsageSpan = document.getElementById('memory-usage');
        this.typingIndicator = document.getElementById('typing-indicator');
        this.loadingOverlay = document.getElementById('loading-overlay');
        this.loadingText = document.getElementById('loading-text');
    }
    
    setupEventListeners() {
        // Model selection
        this.modelSelect.addEventListener('change', () => {
            this.loadModelBtn.disabled = !this.modelSelect.value;
        });
        
        // Model loading
        this.loadModelBtn.addEventListener('click', () => this.loadModel());
        this.unloadModelBtn.addEventListener('click', () => this.unloadModel());
        
        // Chat functionality
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        this.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        this.clearChatBtn.addEventListener('click', () => this.clearChat());
        
        // Model card clicks
        document.querySelectorAll('.model-card').forEach(card => {
            card.addEventListener('click', () => {
                const modelName = card.dataset.model;
                this.modelSelect.value = modelName;
                this.loadModelBtn.disabled = false;
            });
        });
        
        // Socket events
        this.socket.on('response_chunk', (data) => this.handleResponseChunk(data));
        this.socket.on('response_complete', (data) => this.handleResponseComplete(data));
        this.socket.on('error', (data) => this.handleError(data));
    }
    
    async loadModels() {
        try {
            const response = await fetch('/api/models');
            const models = await response.json();
            
            this.modelSelect.innerHTML = '<option value="">Select a model...</option>';
            
            Object.entries(models).forEach(([key, model]) => {
                const option = document.createElement('option');
                option.value = key;
                option.textContent = `${model.name} ${model.file_exists ? '' : '(File not found)'}`;
                option.disabled = !model.file_exists;
                this.modelSelect.appendChild(option);
                
                // Update model card status
                const statusElement = document.getElementById(`status-${key}`);
                if (statusElement) {
                    if (model.loaded) {
                        statusElement.textContent = 'Loaded';
                        statusElement.className = 'model-status status-loaded';
                    } else if (!model.file_exists) {
                        statusElement.textContent = 'File not found';
                        statusElement.className = 'model-status status-not-loaded';
                    } else {
                        statusElement.textContent = 'Not loaded';
                        statusElement.className = 'model-status status-not-loaded';
                    }
                }
            });
        } catch (error) {
            console.error('Error loading models:', error);
        }
    }
    
    async loadModel() {
        const modelName = this.modelSelect.value;
        if (!modelName) return;
        
        this.showLoading('Loading model... This may take a few minutes.');
        
        try {
            const response = await fetch('/api/load_model', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ model_name: modelName })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.currentModel = modelName;
                this.enableChat();
                this.clearWelcomeMessage();
                this.addSystemMessage(`✅ ${modelName} loaded successfully!`);
            } else {
                this.addSystemMessage(`❌ Error loading model: ${result.error}`);
            }
        } catch (error) {
            this.addSystemMessage(`❌ Error loading model: ${error.message}`);
        } finally {
            this.hideLoading();
            this.loadModels();
            this.updateStatus();
        }
    }
    
    async unloadModel() {
        this.showLoading('Unloading model...');
        
        try {
            const response = await fetch('/api/unload_model', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.currentModel = null;
                this.disableChat();
                this.addSystemMessage('🔄 Model unloaded successfully!');
            } else {
                this.addSystemMessage(`❌ Error unloading model: ${result.error}`);
            }
        } catch (error) {
            this.addSystemMessage(`❌ Error unloading model: ${error.message}`);
        } finally {
            this.hideLoading();
            this.loadModels();
            this.updateStatus();
        }
    }
    
    async updateStatus() {
        try {
            const response = await fetch('/api/status');
            const status = await response.json();
            
            if (status.current_model) {
                this.currentModelSpan.textContent = `Current: ${status.current_model}`;
                this.unloadModelBtn.disabled = false;
            } else {
                this.currentModelSpan.textContent = 'No model loaded';
                this.unloadModelBtn.disabled = true;
            }
            
            const memoryPercent = status.memory_usage.percent.toFixed(1);
            const memoryGB = (status.memory_usage.total / (1024**3)).toFixed(1);
            this.memoryUsageSpan.textContent = `Memory: ${memoryPercent}% of ${memoryGB}GB`;
        } catch (error) {
            console.error('Error updating status:', error);
        }
    }
    
    sendMessage() {
        const message = this.chatInput.value.trim();
        if (!message || this.isTyping) return;
        
        this.addMessage(message, 'user');
        this.chatInput.value = '';
        this.isTyping = true;
        this.currentResponse = '';
        
        this.showTyping();
        this.socket.emit('chat_message', { message: message });
    }
    
    handleResponseChunk(data) {
        if (!this.isTyping) return;
        
        this.currentResponse += data.chunk;
        this.updateCurrentResponse();
    }
    
    handleResponseComplete(data) {
        this.isTyping = false;
        this.hideTyping();
        this.finalizeResponse();
    }
    
    handleError(data) {
        this.isTyping = false;
        this.hideTyping();
        this.addSystemMessage(`❌ Error: ${data.message}`);
    }
    
    addMessage(content, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        messageDiv.textContent = content;
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    addSystemMessage(content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message system-message';
        messageDiv.style.background = '#fef5e7';
        messageDiv.style.color = '#744210';
        messageDiv.style.textAlign = 'center';
        messageDiv.style.fontStyle = 'italic';
        messageDiv.textContent = content;
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    updateCurrentResponse() {
        let responseElement = document.querySelector('.current-response');
        if (!responseElement) {
            responseElement = document.createElement('div');
            responseElement.className = 'message assistant-message current-response';
            this.chatMessages.appendChild(responseElement);
        }
        responseElement.textContent = this.currentResponse;
        this.scrollToBottom();
    }
    
    finalizeResponse() {
        const responseElement = document.querySelector('.current-response');
        if (responseElement) {
            responseElement.classList.remove('current-response');
        }
    }
    
    clearChat() {
        this.chatMessages.innerHTML = '';
        if (!this.currentModel) {
            this.showWelcomeMessage();
        }
    }
    
    clearWelcomeMessage() {
        const welcomeMessage = document.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
    }
    
    showWelcomeMessage() {
        this.chatMessages.innerHTML = `
            <div class="welcome-message">
                <h3>Welcome to GPT-OSS Chat Interface!</h3>
                <p>Please select and load a model to start chatting.</p>
                <div class="model-cards">
                    <div class="model-card" data-model="gpt-oss-20b">
                        <h4>GPT-OSS 20B</h4>
                        <p>20GB model with F16 precision</p>
                        <span class="model-status status-not-loaded" id="status-gpt-oss-20b">Not loaded</span>
                    </div>
                    <div class="model-card" data-model="gpt-oss-120b">
                        <h4>GPT-OSS 120B</h4>
                        <p>120GB model with Q2_K quantization</p>
                        <span class="model-status status-not-loaded" id="status-gpt-oss-120b">Not loaded</span>
                    </div>
                </div>
            </div>
        `;
    }
    
    enableChat() {
        this.chatInput.disabled = false;
        this.sendBtn.disabled = false;
        this.chatInput.placeholder = 'Type your message here...';
    }
    
    disableChat() {
        this.chatInput.disabled = true;
        this.sendBtn.disabled = true;
        this.chatInput.placeholder = 'Load a model to start chatting...';
    }
    
    showTyping() {
        this.typingIndicator.style.display = 'block';
        this.sendBtn.disabled = true;
    }
    
    hideTyping() {
        this.typingIndicator.style.display = 'none';
        this.sendBtn.disabled = false;
    }
    
    showLoading(text) {
        this.loadingText.textContent = text;
        this.loadingOverlay.style.display = 'flex';
    }
    
    hideLoading() {
        this.loadingOverlay.style.display = 'none';
    }
    
    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
}

// Initialize the chat interface when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new ChatInterface();
});
