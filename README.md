# GPT-OSS Web Interface

A web-based chat interface for running GPT-OSS models locally using GGUF format.

## Features

- 🤖 Support for multiple GPT-OSS models (20B and 120B)
- 💬 Real-time streaming chat interface
- 🔄 Dynamic model loading/unloading
- 📊 Memory usage monitoring
- 🎨 Modern, responsive web UI
- ⚡ WebSocket-based real-time communication

## Requirements

- Python 3.8+
- At least 16GB RAM (32GB+ recommended for 120B model)
- GGUF model files

## Installation

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Ensure your model files are in the project directory:**
   - `gpt-oss-20b-F16.gguf` (20GB model)
   - `gpt-oss-120b-Q2_K-00001-of-00002.gguf` (120GB model part 1)
   - `gpt-oss-120b-Q2_K-00002-of-00002.gguf` (120GB model part 2)

## Usage

### Quick Start

```bash
python start_server.py
```

### Manual Start

```bash
python app.py
```

The web interface will be available at: http://localhost:8080

## Model Configuration

Models are configured in `config.py`. You can adjust:

- Context length
- Temperature and sampling parameters
- GPU/CPU usage
- Memory settings

### Memory Recommendations

- **20B Model**: 16GB+ RAM
- **120B Model**: 32GB+ RAM (CPU inference)

## Web Interface Usage

1. **Load a Model**: Select a model from the dropdown and click "Load Model"
2. **Start Chatting**: Once loaded, type your message and press Enter or click Send
3. **Switch Models**: Unload the current model and load a different one
4. **Monitor Resources**: Check memory usage in the status bar

## API Endpoints

- `GET /api/models` - List available models
- `POST /api/load_model` - Load a specific model
- `POST /api/unload_model` - Unload current model
- `GET /api/status` - Get system and model status

## WebSocket Events

- `chat_message` - Send a chat message
- `response_chunk` - Receive streaming response chunks
- `response_complete` - Response generation complete
- `error` - Error messages

## Troubleshooting

### Common Issues

1. **Out of Memory**: 
   - Try the smaller 20B model first
   - Reduce `n_ctx` in config.py
   - Enable `low_memory_mode` in config.py

2. **Model Loading Fails**:
   - Check file paths in config.py
   - Ensure GGUF files are not corrupted
   - Verify sufficient disk space

3. **Slow Performance**:
   - Reduce `n_batch` size
   - Use GPU acceleration if available
   - Close other applications

### Performance Tips

- Use GPU acceleration when available (`n_gpu_layers > 0`)
- Adjust batch size based on available memory
- Enable memory mapping (`mmap: True`)
- Use quantized models for better performance

## File Structure

```
GPT-OSS/
├── app.py                 # Main Flask application
├── config.py             # Model and server configuration
├── start_server.py       # Startup script
├── requirements.txt      # Python dependencies
├── README.md            # This file
├── templates/
│   └── index.html       # Web interface template
├── static/
│   ├── css/
│   │   └── style.css    # Styling
│   └── js/
│       └── app.js       # Frontend JavaScript
└── *.gguf               # Model files
```

## License

This project is open source. Please check the license of the GPT-OSS models separately.

## Contributing

Feel free to submit issues and enhancement requests!
