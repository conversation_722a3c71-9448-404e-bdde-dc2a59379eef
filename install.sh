#!/bin/bash

echo "🚀 GPT-OSS Web Interface Installation Script"
echo "============================================"

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+ first."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip first."
    exit 1
fi

echo "✅ pip3 found"

# Install requirements
echo "📦 Installing Python dependencies..."
pip3 install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Check model files
echo "🔍 Checking for model files..."

if [ -f "gpt-oss-20b-F16.gguf" ]; then
    size=$(du -h "gpt-oss-20b-F16.gguf" | cut -f1)
    echo "✅ GPT-OSS 20B model found ($size)"
else
    echo "⚠️  GPT-OSS 20B model not found (gpt-oss-20b-F16.gguf)"
fi

if [ -f "gpt-oss-120b-Q2_K-00001-of-00002.gguf" ] && [ -f "gpt-oss-120b-Q2_K-00002-of-00002.gguf" ]; then
    size1=$(du -h "gpt-oss-120b-Q2_K-00001-of-00002.gguf" | cut -f1)
    size2=$(du -h "gpt-oss-120b-Q2_K-00002-of-00002.gguf" | cut -f1)
    echo "✅ GPT-OSS 120B model found (Part 1: $size1, Part 2: $size2)"
else
    echo "⚠️  GPT-OSS 120B model parts not found"
fi

# Make scripts executable
chmod +x start_server.py

echo ""
echo "🎉 Installation complete!"
echo ""
echo "To start the web interface:"
echo "  python3 start_server.py"
echo ""
echo "Or manually:"
echo "  python3 app.py"
echo ""
echo "Then open: http://localhost:5000"
