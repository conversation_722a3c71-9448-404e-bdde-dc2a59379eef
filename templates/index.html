<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT-OSS Web Interface</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🤖 GPT-OSS Web Interface</h1>
            <div class="status-bar">
                <div class="model-info">
                    <span id="current-model">No model loaded</span>
                    <span id="memory-usage"></span>
                </div>
                <div class="controls">
                    <select id="model-select">
                        <option value="">Select a model...</option>
                    </select>
                    <button id="load-model-btn" disabled>Load Model</button>
                    <button id="unload-model-btn" disabled>Unload Model</button>
                </div>
            </div>
        </header>

        <main>
            <div class="chat-container">
                <div id="chat-messages" class="chat-messages">
                    <div class="welcome-message">
                        <h3>Welcome to GPT-OSS Chat Interface!</h3>
                        <p>Please select and load a model to start chatting.</p>
                        <div class="model-cards">
                            <div class="model-card" data-model="gpt-oss-20b">
                                <h4>GPT-OSS 20B</h4>
                                <p>20GB model with F16 precision</p>
                                <span class="model-status" id="status-gpt-oss-20b">Not loaded</span>
                            </div>
                            <div class="model-card" data-model="gpt-oss-120b">
                                <h4>GPT-OSS 120B</h4>
                                <p>120GB model with Q2_K quantization</p>
                                <span class="model-status" id="status-gpt-oss-120b">Not loaded</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="chat-input-container">
                    <div class="input-wrapper">
                        <textarea id="chat-input" placeholder="Type your message here..." rows="3" disabled></textarea>
                        <button id="send-btn" disabled>
                            <span>Send</span>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                            </svg>
                        </button>
                    </div>
                    <div class="input-controls">
                        <button id="clear-chat-btn">Clear Chat</button>
                        <span id="typing-indicator" style="display: none;">AI is typing...</span>
                    </div>
                </div>
            </div>
        </main>

        <div id="loading-overlay" class="loading-overlay" style="display: none;">
            <div class="loading-content">
                <div class="spinner"></div>
                <p id="loading-text">Loading model...</p>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
