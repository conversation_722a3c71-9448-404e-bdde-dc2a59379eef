#!/usr/bin/env python3
"""
GPT-OSS Web Interface Startup Script
"""

import os
import sys
import subprocess
import platform

def check_requirements():
    """Check if all requirements are installed"""
    try:
        import llama_cpp
        import flask
        import flask_cors
        import flask_socketio
        import psutil
        print("✅ All required packages are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        print("Please install requirements with: pip install -r requirements.txt")
        return False

def check_models():
    """Check if model files exist"""
    models = {
        "GPT-OSS 20B": "gpt-oss-20b-F16.gguf",
        "GPT-OSS 120B Part 1": "gpt-oss-120b-Q2_K-00001-of-00002.gguf",
        "GPT-OSS 120B Part 2": "gpt-oss-120b-Q2_K-00002-of-00002.gguf"
    }
    
    missing_models = []
    for name, path in models.items():
        if os.path.exists(path):
            size_gb = os.path.getsize(path) / (1024**3)
            print(f"✅ {name}: {path} ({size_gb:.1f} GB)")
        else:
            print(f"❌ {name}: {path} (NOT FOUND)")
            missing_models.append(name)
    
    if missing_models:
        print(f"\n⚠️  Warning: {len(missing_models)} model file(s) not found")
        print("The web interface will still start, but these models won't be available")
    
    return len(missing_models) == 0

def get_system_info():
    """Display system information"""
    import psutil
    
    memory = psutil.virtual_memory()
    print(f"\n🖥️  System Information:")
    print(f"   Platform: {platform.system()} {platform.release()}")
    print(f"   CPU Cores: {psutil.cpu_count()}")
    print(f"   Total RAM: {memory.total / (1024**3):.1f} GB")
    print(f"   Available RAM: {memory.available / (1024**3):.1f} GB")
    print(f"   Memory Usage: {memory.percent}%")
    
    # Check for GPU
    try:
        import GPUtil
        gpus = GPUtil.getGPUs()
        if gpus:
            for i, gpu in enumerate(gpus):
                print(f"   GPU {i}: {gpu.name} ({gpu.memoryTotal}MB)")
        else:
            print("   GPU: None detected")
    except ImportError:
        print("   GPU: Detection unavailable (install gputil for GPU info)")

def main():
    print("🚀 Starting GPT-OSS Web Interface")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check models
    check_models()
    
    # Display system info
    get_system_info()
    
    print("\n🌐 Starting web server...")
    print("   URL: http://localhost:5000")
    print("   Press Ctrl+C to stop the server")
    print("=" * 50)
    
    # Start the Flask application
    try:
        from app import app, socketio, SERVER_CONFIG
        socketio.run(app, 
                    host=SERVER_CONFIG['host'], 
                    port=SERVER_CONFIG['port'], 
                    debug=False)  # Set to False for production
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
