from flask import Flask, render_template, request, jsonify, Response
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit
import json
import threading
import time
import gc
import psutil
import os
from datetime import datetime
from config import MODELS, SERVER_CONFIG, CHAT_CONFIG, MEMORY_CONFIG

try:
    from llama_cpp import <PERSON><PERSON><PERSON>
except ImportError:
    print("llama-cpp-python not installed. Please run: pip install -r requirements.txt")
    exit(1)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*")

class ModelManager:
    def __init__(self):
        self.current_model = None
        self.current_model_name = None
        self.chat_history = []
        self.model_lock = threading.Lock()
    
    def load_model(self, model_name):
        """Load a specific model"""
        with self.model_lock:
            if model_name not in MODELS:
                raise ValueError(f"Model {model_name} not found")
            
            # Unload current model if auto_unload is enabled
            if MEMORY_CONFIG['auto_unload'] and self.current_model:
                self.unload_model()
            
            model_config = MODELS[model_name]
            
            # Check if model file exists
            if not os.path.exists(model_config['path']):
                raise FileNotFoundError(f"Model file not found: {model_config['path']}")
            
            print(f"Loading model: {model_config['name']}")
            
            try:
                print(f"Attempting to load model with parameters:")
                print(f"  - Path: {model_config['path']}")
                print(f"  - Context: {model_config['n_ctx']}")
                print(f"  - Batch: {model_config['n_batch']}")
                print(f"  - GPU layers: {model_config['n_gpu_layers']}")

                self.current_model = Llama(
                    model_path=model_config['path'],
                    n_ctx=model_config['n_ctx'],
                    n_batch=model_config['n_batch'],
                    n_gpu_layers=model_config['n_gpu_layers'],
                    use_mmap=MEMORY_CONFIG['mmap'],
                    use_mlock=MEMORY_CONFIG['mlock'],
                    verbose=True,  # Enable verbose for debugging
                    n_threads=4,   # Limit threads for stability
                    f16_kv=True    # Use f16 for key-value cache
                )
                self.current_model_name = model_name
                print(f"Successfully loaded: {model_config['name']}")
                return True
            except Exception as e:
                print(f"Error loading model: {str(e)}")
                print(f"Error type: {type(e).__name__}")
                import traceback
                traceback.print_exc()
                self.current_model = None
                self.current_model_name = None
                raise e
    
    def unload_model(self):
        """Unload the current model"""
        with self.model_lock:
            if self.current_model:
                del self.current_model
                self.current_model = None
                self.current_model_name = None
                gc.collect()
                print("Model unloaded")
    
    def generate_response(self, message, stream=True):
        """Generate response from the current model"""
        if not self.current_model:
            raise RuntimeError("No model loaded")
        
        model_config = MODELS[self.current_model_name]
        
        # Prepare the prompt with chat history
        prompt = self._prepare_prompt(message)
        
        try:
            if stream:
                return self.current_model(
                    prompt,
                    max_tokens=model_config['max_tokens'],
                    temperature=model_config['temperature'],
                    top_p=model_config['top_p'],
                    top_k=model_config['top_k'],
                    repeat_penalty=model_config['repeat_penalty'],
                    stream=True
                )
            else:
                response = self.current_model(
                    prompt,
                    max_tokens=model_config['max_tokens'],
                    temperature=model_config['temperature'],
                    top_p=model_config['top_p'],
                    top_k=model_config['top_k'],
                    repeat_penalty=model_config['repeat_penalty'],
                    stream=False
                )
                return response['choices'][0]['text']
        except Exception as e:
            print(f"Error generating response: {str(e)}")
            raise e
    
    def _prepare_prompt(self, message):
        """Prepare prompt with chat history"""
        prompt = CHAT_CONFIG['system_prompt'] + "\n\n"
        
        # Add recent chat history
        for entry in self.chat_history[-CHAT_CONFIG['max_history']:]:
            prompt += f"Human: {entry['user']}\nAssistant: {entry['assistant']}\n\n"
        
        prompt += f"Human: {message}\nAssistant:"
        return prompt
    
    def add_to_history(self, user_message, assistant_response):
        """Add conversation to history"""
        self.chat_history.append({
            'user': user_message,
            'assistant': assistant_response,
            'timestamp': datetime.now().isoformat()
        })
        
        # Trim history if too long
        if len(self.chat_history) > CHAT_CONFIG['max_history']:
            self.chat_history = self.chat_history[-CHAT_CONFIG['max_history']:]

# Initialize model manager
model_manager = ModelManager()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/models', methods=['GET'])
def get_models():
    """Get available models"""
    models_info = {}
    for key, config in MODELS.items():
        models_info[key] = {
            'name': config['name'],
            'description': config['description'],
            'loaded': model_manager.current_model_name == key,
            'file_exists': os.path.exists(config['path'])
        }
    return jsonify(models_info)

@app.route('/api/load_model', methods=['POST'])
def load_model():
    """Load a specific model"""
    data = request.get_json()
    model_name = data.get('model_name')
    
    if not model_name:
        return jsonify({'error': 'Model name required'}), 400
    
    try:
        model_manager.load_model(model_name)
        return jsonify({'success': True, 'message': f'Model {model_name} loaded successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/unload_model', methods=['POST'])
def unload_model():
    """Unload current model"""
    try:
        model_manager.unload_model()
        return jsonify({'success': True, 'message': 'Model unloaded successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status', methods=['GET'])
def get_status():
    """Get system and model status"""
    memory = psutil.virtual_memory()
    return jsonify({
        'current_model': model_manager.current_model_name,
        'model_loaded': model_manager.current_model is not None,
        'memory_usage': {
            'total': memory.total,
            'available': memory.available,
            'percent': memory.percent
        },
        'chat_history_length': len(model_manager.chat_history)
    })

@socketio.on('chat_message')
def handle_chat_message(data):
    """Handle chat message with streaming response"""
    message = data.get('message', '')
    
    if not model_manager.current_model:
        emit('error', {'message': 'No model loaded. Please load a model first.'})
        return
    
    try:
        # Generate streaming response
        response_text = ""
        for chunk in model_manager.generate_response(message, stream=True):
            if 'choices' in chunk and len(chunk['choices']) > 0:
                token = chunk['choices'][0].get('text', '')
                response_text += token
                emit('response_chunk', {'chunk': token})
        
        # Add to history
        model_manager.add_to_history(message, response_text)
        emit('response_complete', {'full_response': response_text})
        
    except Exception as e:
        emit('error', {'message': str(e)})

if __name__ == '__main__':
    print("Starting GPT-OSS Web Interface...")
    print(f"Available models: {list(MODELS.keys())}")
    socketio.run(app, 
                host=SERVER_CONFIG['host'], 
                port=SERVER_CONFIG['port'], 
                debug=SERVER_CONFIG['debug'])
