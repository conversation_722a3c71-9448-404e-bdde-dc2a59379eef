#!/usr/bin/env python3
"""
Simple test script to verify model loading works
"""

import os
import sys
from llama_cpp import <PERSON><PERSON><PERSON>

def test_model_loading():
    model_path = "gpt-oss-20b-F16.gguf"
    
    if not os.path.exists(model_path):
        print(f"❌ Model file not found: {model_path}")
        return False
    
    print(f"🔍 Testing model loading: {model_path}")
    print(f"📁 File size: {os.path.getsize(model_path) / (1024**3):.1f} GB")
    
    try:
        print("🚀 Attempting to load model with minimal parameters...")
        
        # Try with very conservative settings first
        model = Llama(
            model_path=model_path,
            n_ctx=512,      # Very small context
            n_batch=32,     # Very small batch
            n_gpu_layers=0, # CPU only
            use_mmap=True,
            use_mlock=False,
            verbose=True,
            n_threads=2     # Limited threads
        )
        
        print("✅ Model loaded successfully!")
        
        # Test a simple generation
        print("🧪 Testing text generation...")
        response = model("Hello, how are you?", max_tokens=50, temperature=0.7)
        print(f"📝 Response: {response['choices'][0]['text']}")
        
        # Clean up
        del model
        print("🧹 Model unloaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error loading model: {str(e)}")
        print(f"❌ Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_model_loading()
    sys.exit(0 if success else 1)
