import os

# Model configurations
MODELS = {
    "gpt-oss-20b": {
        "name": "GPT-OSS 20B",
        "path": "gpt-oss-20b-F16.gguf",
        "description": "20GB GPT-OSS model with F16 precision",
        "max_tokens": 2048,
        "context_length": 4096,
        "n_gpu_layers": -1,  # Use all GPU layers for M2 Max
        "n_ctx": 4096,
        "n_batch": 512,  # Larger batch for GPU
        "temperature": 0.7,
        "top_p": 0.9,
        "top_k": 40,
        "repeat_penalty": 1.1
    },
    "gpt-oss-120b": {
        "name": "GPT-OSS 120B",
        "path": "gpt-oss-120b-Q2_K-00001-of-00002.gguf",  # Main file, llama.cpp will find the second part
        "description": "120GB GPT-OSS model with Q2_K quantization",
        "max_tokens": 2048,
        "context_length": 4096,
        "n_gpu_layers": 20,  # Partial GPU offloading for large model
        "n_ctx": 4096,
        "n_batch": 256,  # Moderate batch size
        "temperature": 0.7,
        "top_p": 0.9,
        "top_k": 40,
        "repeat_penalty": 1.1
    }
}

# Server configuration
SERVER_CONFIG = {
    "host": "0.0.0.0",
    "port": 8080,
    "debug": True
}

# Chat settings
CHAT_CONFIG = {
    "max_history": 20,  # Maximum number of messages to keep in history
    "stream": True,     # Enable streaming responses
    "system_prompt": "You are a helpful AI assistant."
}

# Memory management
MEMORY_CONFIG = {
    "auto_unload": True,  # Automatically unload models when switching
    "low_memory_mode": False,  # Enable for systems with limited RAM
    "mmap": True,  # Use memory mapping for efficiency
    "mlock": False  # Lock model in memory (disable for large models)
}
