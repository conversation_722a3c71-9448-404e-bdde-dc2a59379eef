#!/usr/bin/env python3
"""
Test GPU performance vs CPU performance
"""

import time
from llama_cpp import <PERSON><PERSON><PERSON>

def test_performance():
    model_path = "gpt-oss-20b-F16.gguf"
    
    print("🚀 Testing GPU vs CPU Performance")
    print("=" * 50)
    
    # Test with GPU acceleration
    print("📱 Loading model with GPU acceleration...")
    start_time = time.time()
    
    model_gpu = Llama(
        model_path=model_path,
        n_ctx=2048,
        n_batch=512,
        n_gpu_layers=-1,  # All layers on GPU
        use_mmap=True,
        use_mlock=False,
        verbose=False,
        flash_attn=True
    )
    
    load_time_gpu = time.time() - start_time
    print(f"✅ GPU model loaded in {load_time_gpu:.1f} seconds")
    
    # Test generation speed
    prompt = "Explain quantum computing in simple terms:"
    
    print("🧪 Testing generation speed with GPU...")
    start_time = time.time()
    
    response = model_gpu(prompt, max_tokens=100, temperature=0.7)
    
    gen_time_gpu = time.time() - start_time
    tokens_generated = len(response['choices'][0]['text'].split())
    tokens_per_second_gpu = tokens_generated / gen_time_gpu
    
    print(f"✅ GPU generation: {tokens_generated} tokens in {gen_time_gpu:.1f}s ({tokens_per_second_gpu:.1f} tokens/sec)")
    print(f"📝 Response preview: {response['choices'][0]['text'][:100]}...")
    
    # Clean up
    del model_gpu
    
    print("\n🎯 Performance Summary:")
    print(f"   GPU Load Time: {load_time_gpu:.1f} seconds")
    print(f"   GPU Speed: {tokens_per_second_gpu:.1f} tokens/second")
    
    if tokens_per_second_gpu > 10:
        print("🎉 Excellent performance! GPU acceleration is working well.")
    elif tokens_per_second_gpu > 5:
        print("✅ Good performance! GPU acceleration is helping.")
    else:
        print("⚠️  Performance could be better. Check GPU utilization.")

if __name__ == "__main__":
    test_performance()
